// qgraphicslinearlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsLinearLayout : QGraphicsLayout
{
%TypeHeaderCode
#include <qgraphicslinearlayout.h>
%End

public:
    QGraphicsLinearLayout(QGraphicsLayoutItem *parent /TransferThis/ = 0);
    QGraphicsLinearLayout(Qt::Orientation orientation, QGraphicsLayoutItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsLinearLayout();
    void setOrientation(Qt::Orientation orientation);
    Qt::Orientation orientation() const;
    void addItem(QGraphicsLayoutItem *item /Transfer/);
    void addStretch(int stretch = 1);
    void insertItem(int index, QGraphicsLayoutItem *item /Transfer/);
    void insertStretch(int index, int stretch = 1);
    void removeItem(QGraphicsLayoutItem *item /TransferBack/);
    virtual void removeAt(int index);
%MethodCode
        // The ownership of any existing item must be passed back to Python.
        QGraphicsLayoutItem *itm;
        
        if (a0 < sipCpp->count())
            itm = sipCpp->itemAt(a0);
        else
            itm = 0;
        
        Py_BEGIN_ALLOW_THREADS
        sipSelfWasArg ? sipCpp->QGraphicsLinearLayout::removeAt(a0)
                      : sipCpp->removeAt(a0);
        Py_END_ALLOW_THREADS
        
        // The Qt documentation isn't quite correct as ownership isn't always passed
        // back to the caller.
        if (itm && !itm->parentLayoutItem())
        {
            PyObject *itmo = sipGetPyObject(itm, sipType_QGraphicsLayoutItem);
        
            if (itmo)
                sipTransferBack(itmo);
        }
%End

    void setSpacing(qreal spacing);
    qreal spacing() const;
    void setItemSpacing(int index, qreal spacing);
    qreal itemSpacing(int index) const;
    void setStretchFactor(QGraphicsLayoutItem *item, int stretch);
    int stretchFactor(QGraphicsLayoutItem *item) const;
    void setAlignment(QGraphicsLayoutItem *item, Qt::Alignment alignment);
    Qt::Alignment alignment(QGraphicsLayoutItem *item) const;
    virtual void setGeometry(const QRectF &rect);
    virtual int count() const;
    virtual QGraphicsLayoutItem *itemAt(int index) const;
    virtual void invalidate();
    virtual QSizeF sizeHint(Qt::SizeHint which, const QSizeF &constraint = QSizeF()) const;
};
