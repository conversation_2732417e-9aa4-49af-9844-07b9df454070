// qundogroup.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUndoGroup : QObject
{
%TypeHeaderCode
#include <qundogroup.h>
%End

public:
    explicit QUndoGroup(QObject *parent /TransferThis/ = 0);
    virtual ~QUndoGroup();
    void addStack(QUndoStack *stack);
    void removeStack(QUndoStack *stack);
    QList<QUndoStack *> stacks() const;
    QUndoStack *activeStack() const;
    QAction *createRedoAction(QObject *parent /TransferThis/, const QString &prefix = QString()) const /Factory/;
    QAction *createUndoAction(QObject *parent /TransferThis/, const QString &prefix = QString()) const /Factory/;
    bool canUndo() const;
    bool canRedo() const;
    QString undoText() const;
    QString redoText() const;
    bool isClean() const;

public slots:
    void redo();
    void setActiveStack(QUndoStack *stack);
    void undo();

signals:
    void activeStackChanged(QUndoStack *stack);
    void canRedoChanged(bool canRedo);
    void canUndoChanged(bool canUndo);
    void cleanChanged(bool clean);
    void indexChanged(int idx);
    void redoTextChanged(const QString &redoText);
    void undoTextChanged(const QString &undoText);
};
