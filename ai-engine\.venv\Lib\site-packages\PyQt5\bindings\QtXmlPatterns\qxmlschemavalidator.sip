// qxmlschemavalidator.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlSchemaValidator
{
%TypeHeaderCode
#include <qxmlschemavalidator.h>
%End

public:
    QXmlSchemaValidator();
    QXmlSchemaValidator(const QXmlSchema &schema);
    ~QXmlSchemaValidator();
    void setSchema(const QXmlSchema &schema);
    bool validate(const QUrl &source) const /ReleaseGIL/;
    bool validate(QIODevice *source, const QUrl &documentUri = QUrl()) const /ReleaseGIL/;
    bool validate(const QByteArray &data, const QUrl &documentUri = QUrl()) const;
    QXmlNamePool namePool() const;
    QXmlSchema schema() const;
%MethodCode
        // For reasons we don't quite understand QXmlSchema's copy ctor has to be
        // private as far as sip is concerned - otherwise we get compiler errors.
        // However that means that sip generates the wrong code here, because it
        // doesn't realise it can take a copy of the result.
        
        Py_BEGIN_ALLOW_THREADS
        sipRes = new QXmlSchema(sipCpp->schema());
        Py_END_ALLOW_THREADS
%End

    void setMessageHandler(QAbstractMessageHandler *handler /KeepReference/);
    QAbstractMessageHandler *messageHandler() const;
    void setUriResolver(const QAbstractUriResolver *resolver /KeepReference/);
    const QAbstractUriResolver *uriResolver() const;
    void setNetworkAccessManager(QNetworkAccessManager *networkmanager /KeepReference/);
    QNetworkAccessManager *networkAccessManager() const;

private:
    QXmlSchemaValidator(const QXmlSchemaValidator &);
};
