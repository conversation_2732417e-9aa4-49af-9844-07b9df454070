// qmenubar.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMenuBar : QWidget
{
%TypeHeaderCode
#include <qmenubar.h>
%End

public:
    explicit QMenuBar(QWidget *parent /TransferThis/ = 0);
    virtual ~QMenuBar();
    void addAction(QAction *action);
    QAction *addAction(const QString &text) /Transfer/;
    QAction *addAction(const QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt5_qtwidgets_get_connection_parts(a1, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

    QAction *addMenu(QMenu *menu);
    QMenu *addMenu(const QString &title) /Transfer/;
    QMenu *addMenu(const QIcon &icon, const QString &title) /Transfer/;
    QAction *addSeparator() /Transfer/;
    QAction *insertMenu(QAction *before, QMenu *menu);
    QAction *insertSeparator(QAction *before) /Transfer/;
    void clear();
    QAction *activeAction() const;
    void setActiveAction(QAction *action);
    void setDefaultUp(bool);
    bool isDefaultUp() const;
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    virtual int heightForWidth(int) const;
    QRect actionGeometry(QAction *) const;
    QAction *actionAt(const QPoint &) const;
    void setCornerWidget(QWidget *widget /Transfer/, Qt::Corner corner = Qt::TopRightCorner);
    QWidget *cornerWidget(Qt::Corner corner = Qt::TopRightCorner) const;
    virtual void setVisible(bool visible);

signals:
    void triggered(QAction *action);
    void hovered(QAction *action);

protected:
    void initStyleOption(QStyleOptionMenuItem *option, const QAction *action) const;
    virtual void changeEvent(QEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void leaveEvent(QEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void actionEvent(QActionEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
    virtual bool event(QEvent *);
    virtual void timerEvent(QTimerEvent *);

public:
    bool isNativeMenuBar() const;
    void setNativeMenuBar(bool nativeMenuBar);
};
