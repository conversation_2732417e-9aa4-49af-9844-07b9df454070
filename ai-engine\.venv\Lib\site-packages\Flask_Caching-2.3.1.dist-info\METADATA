Metadata-Version: 2.2
Name: Flask-Caching
Version: 2.3.1
Summary: Adds caching support to Flask applications.
Home-page: https://github.com/pallets-eco/flask-caching
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: Pallets
Maintainer-email: <EMAIL>
License: BSD
Project-URL: Donate, https://palletsprojects.com/donate
Project-URL: Documentation, https://flask-caching.readthedocs.io
Project-URL: Changes, https://flask-caching.readthedocs.io/en/latest/changelog.html
Project-URL: Source Code, https://github.com/pallets-eco/flask-caching
Project-URL: Issue Tracker, https://github.com/pallets-eco/flask-caching/issues
Project-URL: Twitter, https://twitter.com/PalletsTeam
Project-URL: Chat, https://discord.gg/pallets
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: cachelib>=0.9.0
Requires-Dist: Flask
Dynamic: requires-dist

Flask-Caching
=============

A fork of the `Flask-cache`_ extension which adds easy cache support to Flask.

.. _Flask-cache: https://github.com/thadeusb/flask-cache


Installing
----------

Install and update using `pip`_:

.. code-block:: text

    $ pip install -U flask-caching

.. _pip: https://pip.pypa.io/en/stable/getting-started/


Donate
------

The Pallets organization develops and supports Flask and the libraries
it uses. In order to grow the community of contributors and users, and
allow the maintainers to devote more time to the projects, `please
donate today`_.

.. _please donate today: https://palletsprojects.com/donate


Links
-----

-   Documentation: https://flask-caching.readthedocs.io
-   Changes: https://flask-caching.readthedocs.io/en/latest/changelog.html
-   PyPI Releases: https://pypi.org/project/Flask-Caching/
-   Source Code: https://github.com/pallets-eco/flask-caching
-   Issue Tracker: https://github.com/pallets-eco/flask-caching/issues
-   Twitter: https://twitter.com/PalletsTeam
-   Chat: https://discord.gg/pallets
