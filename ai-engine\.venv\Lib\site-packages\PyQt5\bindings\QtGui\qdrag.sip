// qdrag.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDrag : QObject
{
%TypeHeaderCode
#include <qdrag.h>
%End

public:
    explicit QDrag(QObject *dragSource /TransferThis/);
    virtual ~QDrag();
    Qt::DropAction exec(Qt::DropActions supportedActions = Qt::MoveAction) /PyName=exec_,ReleaseGIL/;
%If (Py_v3)
    Qt::DropAction exec(Qt::DropActions supportedActions = Qt::MoveAction) /ReleaseGIL/;
%End
    Qt::DropAction exec(Qt::DropActions supportedActions, Qt::DropAction defaultDropAction) /PyName=exec_,ReleaseGIL/;
    Qt::DropAction exec(Qt::DropActions supportedActions, Qt::DropAction defaultDropAction) /ReleaseGIL/;
    void setMimeData(QMimeData *data /Transfer/);
    QMimeData *mimeData() const;
    void setPixmap(const QPixmap &);
    QPixmap pixmap() const;
    void setHotSpot(const QPoint &hotspot);
    QPoint hotSpot() const;
    QObject *source() const;
    QObject *target() const;
    void setDragCursor(const QPixmap &cursor, Qt::DropAction action);

signals:
    void actionChanged(Qt::DropAction action);
    void targetChanged(QObject *newTarget);

public:
    QPixmap dragCursor(Qt::DropAction action) const;
    Qt::DropActions supportedActions() const;
    Qt::DropAction defaultAction() const;
%If (Qt_5_7_0 -)
%If (WS_X11 || WS_WIN)
    static void cancel();
%End
%End
};
