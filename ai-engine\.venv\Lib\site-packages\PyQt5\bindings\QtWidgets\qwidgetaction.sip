// qwidgetaction.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWidgetAction : QAction
{
%TypeHeaderCode
#include <qwidgetaction.h>
%End

public:
    explicit QWidgetAction(QObject *parent /TransferThis/);
    virtual ~QWidgetAction();
    void setDefaultWidget(QWidget *w /Transfer/);
    QWidget *defaultWidget() const;
    QWidget *requestWidget(QWidget *parent /TransferThis/) /Factory/;
    void releaseWidget(QWidget *widget /TransferBack/);

protected:
    virtual bool event(QEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
    virtual QWidget *createWidget(QWidget *parent /TransferThis/) /Factory/;
    virtual void deleteWidget(QWidget *widget /Transfer/);
    QList<QWidget *> createdWidgets() const;
};
